#!/usr/bin/env python3
"""
Pickup Process Test Suite
=========================

Comprehensive test script for pickup_process functionality according to screen_communication.md

TESTED ENDPOINTS:
- /storage/pickup - Storage pickup with reservation PIN
- /product/pickup - Product pickup with reservation PIN
- /order/customer/pickup - Customer order pickup with pickup PIN
- /order/employment/courier/pickup-expired - Courier pickup of expired orders
- /order/employment/courier/pickup - Courier pickup of employee orders

WHAT IT TESTS:
1. Database Operations - Creates test records, verifies status updates, cleans up
2. API Communication - Tests HTTP requests, validates responses, handles errors
3. WebSocket Communication - Simulates hardware_screen_ready, verifies message flow
4. Database Verification - Confirms reservation status updates after pickup

PREREQUISITES:
- Backend server running on http://localhost:8000
- Database accessible at ./database.db (adjust DB_PATH if different)
- Required packages: requests, websockets (pip install requests websockets)

USAGE:
python test_pickup_process.py

The script will:
1. Create test records in database
2. Test all pickup endpoints
3. Simulate WebSocket communication
4. Verify database updates
5. Clean up test data
6. Print comprehensive results
"""

import asyncio
import json
import mysql.connector
import requests
import websockets
import logging
from datetime import datetime
from typing import Dict, List
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# ============================================================================
# CONFIGURATION - Adjust these settings for your environment
# ============================================================================

# Server Configuration
BASE_URL = "http://localhost:8000"
WS_URL = "ws://localhost:8000/ws"

# Database Configuration (MySQL from .env file)
DB_CONFIG = {
    "host": os.getenv("DB_HOST", "localhost"),
    "port": int(os.getenv("DB_PORT", "3306")),
    "database": os.getenv("DB_NAME", "box4"),
    "user": os.getenv("DB_USER", "root"),
    "password": os.getenv("DB_PASSWORD", "")
}

# Test Data Configuration
TEST_DATA = {
    "storage": {
        "section_id": 1,
        "pin": "TEST123",
        "size_category": 1
    },
    "product": {
        "section_id": 2,
        "pin": "PROD456",
        "price": 50.0
    },
    "order": {
        "section_id": 3,
        "pin": "ORDER789"
    },
    "employment": {
        "operator_id": 1,
        "expired_sections": [4, 5],
        "employee_sections": [6, 7]
    }
}

# Test Configuration
TEST_CONFIG = {
    "websocket_timeout": 3.0,
    "max_websocket_timeouts": 10,
    "async_wait_time": 5.0,  # Increased wait time for async operations
    "cleanup_after_tests": True
}

# Expected WebSocket Message Flow for pickup_process
EXPECTED_PICKUP_MESSAGES = [
    "pickup_loop_started",
    "opening_section",
    "section_status_changed",  # opened
    "section_status_changed",  # closed
    "pickup_completed"
]

# ============================================================================

class PickupProcessTester:
    def __init__(self):
        self.session = requests.Session()
        self.test_results = []

        # Set up authentication header for product endpoints
        api_token = os.getenv("API_BEARER_TOKEN")
        if api_token:
            self.session.headers.update({"Authorization": f"Bearer {api_token}"})

    def get_db_connection(self):
        """Get MySQL database connection using .env configuration"""
        return mysql.connector.connect(**DB_CONFIG)
        
    def log_test_result(self, test_name: str, success: bool, message: str = ""):
        """Log test result"""
        result = {
            "test": test_name,
            "success": success,
            "message": message,
            "timestamp": datetime.now().isoformat()
        }
        self.test_results.append(result)
        status = "✅ PASS" if success else "❌ FAIL"
        logger.info(f"{status} - {test_name}: {message}")

    def setup_database_records(self):
        """Create test records in database tables"""
        logger.info("Setting up test database records...")

        try:
            # Test database connection
            conn = self.get_db_connection()
            cursor = conn.cursor()
            
            # Create test storage reservation
            storage_data = TEST_DATA["storage"]
            cursor.execute("""
                INSERT INTO storage_reservations
                (id, section_id, reservation_pin, status, created_at, category, price)
                VALUES (%s, %s, %s, 1, %s, %s, 0.0)
                ON DUPLICATE KEY UPDATE
                section_id = VALUES(section_id),
                reservation_pin = VALUES(reservation_pin),
                status = 1,
                category = VALUES(category)
            """, (9999, storage_data["section_id"], storage_data["pin"], datetime.now(), storage_data["size_category"]))

            # Create test sale reservation (product)
            product_data = TEST_DATA["product"]
            cursor.execute("""
                INSERT INTO sale_reservations
                (id, section_id, reservation_pin, status, created_at, price, reserved, quantity)
                VALUES (%s, %s, %s, 1, %s, %s, 1, 1)
                ON DUPLICATE KEY UPDATE
                section_id = VALUES(section_id),
                reservation_pin = VALUES(reservation_pin),
                status = 1,
                price = VALUES(price),
                reserved = 1
            """, (9999, product_data["section_id"], product_data["pin"], datetime.now(), product_data["price"]))

            # Create test order reservation for customer pickup
            order_data = TEST_DATA["order"]
            cursor.execute("""
                INSERT INTO order_reservations
                (id, section_id, pickup_pin, status, expired, created_at, type)
                VALUES (%s, %s, %s, 1, 0, %s, 'customer_order')
                ON DUPLICATE KEY UPDATE
                section_id = VALUES(section_id),
                pickup_pin = VALUES(pickup_pin),
                status = 1,
                expired = 0,
                type = 'customer_order'
            """, (9999, order_data["section_id"], order_data["pin"], datetime.now()))
            
            # Create test expired order reservations for courier pickup
            employment_data = TEST_DATA["employment"]
            for i, section_id in enumerate(employment_data.get("expired_sections", [4, 5])):
                cursor.execute("""
                    INSERT INTO order_reservations
                    (id, section_id, status, expired, created_at, type)
                    VALUES (%s, %s, 1, 0, %s, 'employee_send')
                    ON DUPLICATE KEY UPDATE
                    section_id = VALUES(section_id),
                    status = 1,
                    expired = 0,
                    type = 'employee_send'
                """, (9998 - i, section_id, datetime.now()))

            # Create test employee order reservations for courier pickup
            for i, section_id in enumerate(employment_data.get("employee_sections", [6, 7])):
                cursor.execute("""
                    INSERT INTO order_reservations
                    (id, section_id, status, expired, created_at, type)
                    VALUES (%s, %s, 1, 0, %s, 'employee_send')
                    ON DUPLICATE KEY UPDATE
                    section_id = VALUES(section_id),
                    status = 1,
                    expired = 0,
                    type = 'employee_send'
                """, (9996 - i, section_id, datetime.now()))
            
            conn.commit()
            conn.close()
            
            self.log_test_result("Database Setup", True, "Test records created successfully")
            return {
                "storage_pin": storage_data["pin"],
                "product_pin": product_data["pin"],
                "order_pin": order_data["pin"]
            }
            
        except Exception as e:
            self.log_test_result("Database Setup", False, f"Error: {str(e)}")
            return None

    async def test_websocket_communication(self, session_id: str, expected_messages: List[str]):
        """Test WebSocket communication for pickup process"""
        try:
            uri = f"{WS_URL}/{session_id}"
            async with websockets.connect(uri) as websocket:

                # Collect messages for verification
                received_messages = []
                timeout_count = 0
                max_timeouts = TEST_CONFIG.get("max_websocket_timeouts", 10)  # Increased timeout
                websocket_timeout = TEST_CONFIG.get("websocket_timeout", 3.0)  # Increased timeout
                hardware_screen_ready_sent = False

                while timeout_count < max_timeouts:
                    try:
                        message = await asyncio.wait_for(websocket.recv(), timeout=websocket_timeout)
                        msg_data = json.loads(message)
                        msg_type = msg_data.get("type", "unknown")
                        received_messages.append(msg_type)

                        logger.debug(f"WebSocket received: {msg_type}")

                        # Send hardware_screen_ready when we receive pickup_status (waiting for hardware screen ready)
                        if msg_type == "pickup_status" and not hardware_screen_ready_sent:
                            message_text = msg_data.get("message", "")
                            if "hardware screen ready" in message_text.lower():
                                logger.info(f"Sending hardware_screen_ready for session {session_id}")
                                await websocket.send(json.dumps({
                                    "type": "hardware_screen_ready"
                                }))
                                hardware_screen_ready_sent = True

                        # Check for completion
                        if msg_type in ["pickup_completed", "pickup_timeout", "pickup_error"]:
                            break

                    except asyncio.TimeoutError:
                        timeout_count += 1
                        # If we haven't sent hardware_screen_ready yet, send it now
                        if not hardware_screen_ready_sent:
                            logger.info(f"Timeout - sending hardware_screen_ready for session {session_id}")
                            try:
                                await websocket.send(json.dumps({
                                    "type": "hardware_screen_ready"
                                }))
                                hardware_screen_ready_sent = True
                            except:
                                pass
                        continue

                # Check if we got the key messages indicating pickup process worked
                key_messages = ["pickup_loop_started", "pickup_status"]
                success = any(msg in received_messages for msg in key_messages)
                return success, received_messages

        except Exception as e:
            logger.error(f"WebSocket test error: {e}")
            return False, []

    def test_storage_pickup(self, storage_pin: str):
        """Test /storage/pickup endpoint"""
        logger.info("Testing /storage/pickup endpoint...")
        
        try:
            # Make API request
            response = self.session.post(f"{BASE_URL}/storage/pickup", json={
                "reservation_pin": storage_pin
            })
            
            if response.status_code == 200:
                data = response.json()
                session_id = data.get("session_id")
                
                if session_id and data.get("success"):
                    self.log_test_result("Storage Pickup API", True, f"Session created: {session_id}")
                    return session_id
                else:
                    self.log_test_result("Storage Pickup API", False, "No session ID or failed response")
            else:
                self.log_test_result("Storage Pickup API", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            self.log_test_result("Storage Pickup API", False, f"Error: {str(e)}")
        
        return None

    def test_product_pickup(self, product_pin: str):
        """Test /product/pickup endpoint"""
        logger.info("Testing /product/pickup endpoint...")
        
        try:
            # Make API request
            payload = {
                "section_id": TEST_DATA["product"]["section_id"],
                "reservation_pin": product_pin
            }
            logger.info(f"Product pickup request payload: {payload}")
            response = self.session.post(f"{BASE_URL}/product/pickup", json=payload)
            
            if response.status_code == 200:
                data = response.json()
                session_id = data.get("session_id")

                if session_id and data.get("success"):
                    self.log_test_result("Product Pickup API", True, f"Session created: {session_id}")
                    return session_id
                else:
                    self.log_test_result("Product Pickup API", False, f"No session ID or failed response. Response: {data}")
            else:
                self.log_test_result("Product Pickup API", False, f"HTTP {response.status_code}. Response: {response.text}")
                
        except Exception as e:
            self.log_test_result("Product Pickup API", False, f"Error: {str(e)}")
        
        return None

    def test_order_customer_pickup(self, order_pin: str):
        """Test /order/customer/pickup endpoint"""
        logger.info("Testing /order/customer/pickup endpoint...")
        
        try:
            # Make API request
            response = self.session.post(f"{BASE_URL}/order/customer/pickup", json={
                "pickup_pin": order_pin
            })
            
            if response.status_code == 200:
                data = response.json()
                session_id = data.get("session_id")
                
                if session_id and data.get("success"):
                    self.log_test_result("Order Customer Pickup API", True, f"Session created: {session_id}")
                    return session_id
                else:
                    self.log_test_result("Order Customer Pickup API", False, "No session ID or failed response")
            else:
                self.log_test_result("Order Customer Pickup API", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            self.log_test_result("Order Customer Pickup API", False, f"Error: {str(e)}")
        
        return None

    def test_employment_courier_pickup_expired(self):
        """Test /order/employment/courier/pickup-expired endpoint"""
        logger.info("Testing /order/employment/courier/pickup-expired endpoint...")
        
        try:
            # Make API request
            response = self.session.post(f"{BASE_URL}/order/employment/courier/pickup-expired", json={
                "operator_id": TEST_DATA["employment"]["operator_id"]
            })
            
            if response.status_code == 200:
                data = response.json()
                session_id = data.get("session_id")
                
                if session_id and data.get("success") and data.get("total_sections", 0) > 0:
                    self.log_test_result("Employment Courier Pickup Expired API", True, 
                                       f"Session created: {session_id}, Sections: {data.get('sections', [])}")
                    return session_id
                else:
                    self.log_test_result("Employment Courier Pickup Expired API", True, "No expired orders found")
            else:
                self.log_test_result("Employment Courier Pickup Expired API", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            self.log_test_result("Employment Courier Pickup Expired API", False, f"Error: {str(e)}")
        
        return None

    def test_employment_courier_pickup(self):
        """Test /order/employment/courier/pickup endpoint"""
        logger.info("Testing /order/employment/courier/pickup endpoint...")
        
        try:
            # Make API request
            response = self.session.post(f"{BASE_URL}/order/employment/courier/pickup", json={
                "operator_id": TEST_DATA["employment"]["operator_id"]
            })
            
            if response.status_code == 200:
                data = response.json()
                session_id = data.get("session_id")
                
                if session_id and data.get("success") and data.get("total_sections", 0) > 0:
                    self.log_test_result("Employment Courier Pickup API", True, 
                                       f"Session created: {session_id}, Sections: {data.get('sections', [])}")
                    return session_id
                else:
                    self.log_test_result("Employment Courier Pickup API", True, "No employee orders found")
            else:
                self.log_test_result("Employment Courier Pickup API", False, f"HTTP {response.status_code}")
                
        except Exception as e:
            self.log_test_result("Employment Courier Pickup API", False, f"Error: {str(e)}")
        
        return None

    def verify_database_status_updates(self, test_pins: Dict[str, str]):
        """Verify that database records were updated correctly after pickup"""
        logger.info("Verifying database status updates...")

        try:
            conn = self.get_db_connection()
            cursor = conn.cursor()

            # Check storage reservation status (should be 0 - completed)
            cursor.execute("SELECT status FROM storage_reservations WHERE reservation_pin = %s",
                         (test_pins["storage_pin"],))
            storage_result = cursor.fetchone()

            if storage_result and storage_result[0] == 0:
                self.log_test_result("Storage DB Update", True, "Storage reservation deactivated")
            else:
                self.log_test_result("Storage DB Update", False, f"Storage status: {storage_result}")

            # Check sale reservation status (should be 0 - completed)
            cursor.execute("SELECT status FROM sale_reservations WHERE reservation_pin = %s",
                         (test_pins["product_pin"],))
            product_result = cursor.fetchone()

            if product_result and product_result[0] == 0:
                self.log_test_result("Product DB Update", True, "Sale reservation completed")
            else:
                self.log_test_result("Product DB Update", False, f"Product status: {product_result}")

            # Check order reservation status (should be 0 - completed)
            cursor.execute("SELECT status FROM order_reservations WHERE pickup_pin = %s",
                         (test_pins["order_pin"],))
            order_result = cursor.fetchone()

            if order_result and order_result[0] == 0:
                self.log_test_result("Order DB Update", True, "Order reservation completed")
            else:
                self.log_test_result("Order DB Update", False, f"Order status: {order_result}")

            conn.close()

        except Exception as e:
            self.log_test_result("Database Verification", False, f"Error: {str(e)}")

    async def run_websocket_tests(self, session_ids: Dict[str, str]):
        """Run WebSocket tests for all pickup sessions"""
        logger.info("Running WebSocket communication tests...")

        # Expected message flow for pickup_process
        expected_messages = EXPECTED_PICKUP_MESSAGES

        # Test each session
        for endpoint, session_id in session_ids.items():
            if session_id:
                logger.info(f"Testing WebSocket for {endpoint}...")
                success, messages = await self.test_websocket_communication(session_id, expected_messages)
                self.log_test_result(f"WebSocket {endpoint}", success,
                                   f"Messages: {messages}")

    def cleanup_test_records(self):
        """Clean up test records from database"""
        logger.info("Cleaning up test records...")

        try:
            conn = self.get_db_connection()
            cursor = conn.cursor()

            # Remove test records
            cursor.execute("DELETE FROM storage_reservations WHERE id = 9999")
            cursor.execute("DELETE FROM sale_reservations WHERE id = 9999")
            cursor.execute("DELETE FROM order_reservations WHERE id IN (9999, 9998, 9997, 9996, 9995)")

            conn.commit()
            conn.close()

            self.log_test_result("Cleanup", True, "Test records removed")

        except Exception as e:
            self.log_test_result("Cleanup", False, f"Error: {str(e)}")

    def print_test_summary(self):
        """Print summary of all test results"""
        logger.info("\n" + "="*60)
        logger.info("PICKUP PROCESS TEST SUMMARY")
        logger.info("="*60)

        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests

        logger.info(f"Total Tests: {total_tests}")
        logger.info(f"Passed: {passed_tests}")
        logger.info(f"Failed: {failed_tests}")
        logger.info(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")

        logger.info("\nDetailed Results:")
        for result in self.test_results:
            status = "✅" if result["success"] else "❌"
            logger.info(f"{status} {result['test']}: {result['message']}")

        logger.info("="*60)

    def check_server_availability(self):
        """Check if the backend server is running"""
        try:
            self.session.get(f"{BASE_URL}/", timeout=5)
            self.log_test_result("Server Check", True, f"Server responding at {BASE_URL}")
            return True
        except Exception as e:
            self.log_test_result("Server Check", False, f"Server not available at {BASE_URL}: {str(e)}")
            return False

    async def run_all_tests(self):
        """Run complete test suite"""
        logger.info("Starting Pickup Process Test Suite...")
        logger.info("="*60)

        # Step 0: Check server availability
        if not self.check_server_availability():
            logger.error("Backend server is not available. Please start the server and try again.")
            return

        # Step 1: Setup database records
        test_pins = self.setup_database_records()
        if not test_pins:
            logger.error("Failed to setup test data. Aborting tests.")
            return

        # Step 2: Test all API endpoints
        session_ids = {}

        # Test storage pickup
        session_ids["storage"] = self.test_storage_pickup(test_pins["storage_pin"])

        # Test product pickup
        session_ids["product"] = self.test_product_pickup(test_pins["product_pin"])

        # Test order customer pickup
        session_ids["order_customer"] = self.test_order_customer_pickup(test_pins["order_pin"])

        # Test employment courier pickup (expired)
        session_ids["courier_expired"] = self.test_employment_courier_pickup_expired()

        # Test employment courier pickup
        session_ids["courier_pickup"] = self.test_employment_courier_pickup()

        # Step 3: Test WebSocket communication
        await self.run_websocket_tests(session_ids)

        # Step 4: Wait a bit for async operations to complete
        wait_time = TEST_CONFIG.get("async_wait_time", 5.0)
        logger.info(f"Waiting {wait_time} seconds for async operations to complete...")
        await asyncio.sleep(wait_time)

        # Step 5: Verify database updates
        self.verify_database_status_updates(test_pins)

        # Step 6: Cleanup
        self.cleanup_test_records()

        # Step 7: Print summary
        self.print_test_summary()


async def main():
    """Main test function"""
    tester = PickupProcessTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    print("=" * 70)
    print("PICKUP PROCESS TEST SUITE")
    print("=" * 70)
    print("Testing all endpoints that use pickup_process functionality")
    print("according to screen_communication.md specification")
    print()
    print("PREREQUISITES:")
    print(f"✓ Backend server running on {BASE_URL}")
    print(f"✓ MySQL database: {DB_CONFIG['user']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}")
    print("✓ Required packages: requests, websockets, mysql-connector-python")
    print()
    print("ENDPOINTS TESTED:")
    print("• /storage/pickup")
    print("• /product/pickup")
    print("• /order/customer/pickup")
    print("• /order/employment/courier/pickup-expired")
    print("• /order/employment/courier/pickup")
    print()
    print("Starting tests...")
    print("=" * 70)

    try:
        # Run the test suite
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n❌ Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Test suite failed with error: {e}")
        import traceback
        traceback.print_exc()

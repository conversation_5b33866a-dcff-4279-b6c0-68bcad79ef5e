# Screen Communication Documentation

## Sale module communication


### product/pickup

**Request body:**
```json
{
  "section_id": 1,
  "reservation_pin": "123456"
}
```

**Response body:**
```json
{
    "success": true,
    "session_id": "123e4567-e89b-12d3-a456-426614174000",     // session ID for WebSocket connection
    "status": "flow_started",                               // status of the operation
    "requires_payment": true,                                // does the operation require payment
    "amount": 100,                                          // amount to be paid
    "message": "Sale flow has been initiated, connect to WebSocket",   // message to be shown to user
    "section_id": 1                                         // section ID for the operation
}
```

**Functionality:**
- create websocket session
- if requires_payment == true, wait first for command payment_screen_ready
- success, succesfull_sections = pickup_process(sections=[section_id])
    - if success and section in succesfull_sections, update sale_reservation status to 0
    - close websocket session







## Storage module communication

### storage/categories

**Request body:**
```json
None
```

**Response body:**
```json
{
    "success": true,
    "categories": [             // list of all categories
        {
            "size_category": 1,
            "price": 100,
            "is_available": true        // is there any available section of this category
        },
        {
            "size_category": 2,
            "price": 200,
            "is_available": false
        }
    ]
}
```


### storage/sections

**Request body:**
```json
None
```

**Response body:**
```json
{
    "success": true,
    "products": [               // list of all sections
        {
            "section_id": 1,    // visual ID shown to user (e.g. "Section 1")
            "identification_name": "Section 1",   // same as section_id, for compatibility with other modules
            "tempered": 0,      // is the section tempered glass
            "blocked": 0,       // is the section blocked
            "service": 0,       // is the section a service section
            "mode": "storage",  // operation mode of the section
            "type": "storage",  // type of the section
            "size_width": 50,   // size of the section in cm
            "size_depth": 50,
            "size_height": 50,
            "size_category": 1,  // category of the section
            "is_available": true    // is the section available for reservation
        }
    ],
    "total_count": 1           // total number of sections
}
```

### storage/insert

**Request body:**
```json
{
    "section_id": 1,
    "size_category": 2,
    "email": "<EMAIL>"
}
```

**Response body:**
```json
{
    "success": true,
    "session_id": "123e4567-e89b-12d3-a456-426614174000",     // session ID for WebSocket connection
    "status": "flow_started",                               // status of the operation
    "requires_payment": true,                                // does the operation require payment
    "amount": 100,                                          // amount to be paid
    "message": "Storage flow has been initiated, connect to WebSocket",   // message to be shown to user
    "section_id": 1                                         // section ID for the operation
}
```

**Functionality:**
- create websocket session and wait for command payment_screen_ready and then hardware_screen_ready


### storage/pickup

**Request body:**
```json
{
    "reservation_pin": "123456"
}
```

**Response body:**
```json
{
    "success": true,
    "session_id": "123e4567-e89b-12d3-a456-426614174000",     // session ID for WebSocket connection
    "status": "flow_started",                               // status of the operation
    "requires_payment": true,                                // does the operation require payment
    "amount": 100,                                          // amount to be paid
    "message": "Storage flow has been initiated, connect to WebSocket",   // message to be shown to user
    "section_id": 1,                                        // section ID for the operation
    "hours_since_creation": 24,                             // hours since reservation creation
    "max_storage_hours": 25,                                // maximum storage hours
    "extra_charge_amount": 63                               // extra charge amount per hour
}
```

**Functionality:**
- create websocket session
- if requires_payment == true, wait first for command payment_screen_ready
- success, succesfull_sections = pickup_process(sections=[section_id])
    - if success and section in succesfull_sections, update storage_reservation status to 0
    - close websocket session




## Order module communication

### /order/employment/courier/pickup-expired

**Request body:**
```json
{
    "operatod_id": 1    // operator id, who is picking up the orders
}
```

**Response body:**
```json
{
    "session_id": "1a219d3e-ed9b-411a-92c3-96c11b0bfda5",       // create new websocket session and returns session id
    "success": true,        // if any error occures, returns false
    "message": "Expired orders found",    // success or error message
    "sections": [
        1,4,6               // all sections with expired orders (returns all records in order_reservation table section_ids, where expired==0 and status==1 and type=="employee_send")
    ],
    "total_sections": 3      // total number of sections with expired orders
}
```

**Functionality:**
- if total_sections is greater than 0, it creates session_id, then starts pickup loop
- success, successful_sections = pickup_process(sections=sections, endpoint="/order/employment/courier/pickup-expired")
- if success and section in successful_sections, update order_reservation status to 0
- close websocket session


### /order/employment/courier/pickup

**Request body:**
```json
{
    "operatod_id": 1    // operator id, who is picking up the orders
}
```

**Response body:**
```json
{
    "session_id": "1a219d3e-ed9b-411a-92c3-96c11b0bfda5",       // if total_sections is greater than 0, create new websocket session and returns session id
    "success": true,        // if any error occures or phone number is not valid, returns false
    "message": "",           // success or error message
    "sections": [
        1,4,6               // all sections with employee's orders (returns all records in order_reservation table section_ids, where expired==0 and status==1 and type=="employee_send")
    ],
    "total_sections": 3     // total number of sections with employee's orders
}
```

**Functionality:**
- similar to /order/pickup-expired
- success, successful_sections = pickup_process(sections=sections, endpoint="/order/employment/courier/pickup")
- if success and section in successful_sections, update order_reservation status to 0


### /order/employment/courier/deliver

**Request body:**
```json
{
    "phone_number": "123456789"    // employee's phone number
}
```

**Response body:**
```json
{
    "session_id": "1a219d3e-ed9b-411a-92c3-96c11b0bfda5",       // if phone number is valid, create new websocket session and returns session id
    "success": true,        // if any error occures or phone number is not valid, returns false
    "message": "",           // success or error message
    "section_ids": [1,2,3]          // reserved section id, if not reserved, returns "section_id": null
}
```

**Functionality:**
- after request, it sends request to jetveo server to "/api/employment/deliver" to check if phone number is valid,
  request: {"phone_number": "123456789"}, response: {"success": true, "phone_number": "123456789","valid": true, "section_ids": null/[1,2,3]}
- if number is valid, it calls function select_sections():
  success, selected_section_ids = select_sections()
  - if success==true, it creates new record in order_reservation and saves phone_number and selected_section_ids and "employee_deliver" into type column. Then sends message {"order_deliver": true}.
  - if success==false, it just sends {"order_deliver": false, "message": some_error_message}
- closes websocket session


### /order/employment/customer/send

**Request body:**
```json
{
    "phone_number": "123456789"    // employee's phone number
}
```

**Response body:**
```json
{
    "session_id": "1a219d3e-ed9b-411a-92c3-96c11b0bfda5",       // create new websocket session and returns session id
    "success": true,        // if any error occures, returns false
    "section_id": 1,        // reserved section id, if not reserved, returns "section_id": null
    "valid": true,          // if phone number is valid, returns true, else returns false
    "message": "Employee notified successfully"    // success or error message
}
```

**Functionality:**
- after request, it sends request to jetveo server to "/api/employment/send" to check if phone number is valid, request: {"phone_number": "123456789"}, response: {"phone_number": "123456789","valid": true, "section_id": null/1}
- if phone number is valid, it creates websocket session.
- calls function select_sections():
  success, selected_section_id = select_sections()
  - if success==true, it creates new record in order_reservation and saves phone_number and selected_section_id. Then sends message {"order_send": true}
  - if success==false, it just sends {"order_send": false, "message": some_error_message}
- closes websocket session


### /order/customer/pickup

**Request body:**
```json
{
    "pickup_pin": "123456"    // pickup pin
}
```

**Response body:**
- similar to /product/pickup

**Functionality:**
- for valid pin, check if there is record in order_reservation with matching pin and status==1 and expired==0. check pickup_pin column
- pickup_process(sections=[section_id])


### /order/employment/customer/reclaim

**Request body:**
```json
{
    "reclamation_pin": "1asfd789"    // employee's phone number
}
```

**Response body:**
```json
{
    "session_id": "1a219d3e-ed9b-411a-92c3-96c11b0bfda5",       // create new websocket session and returns session id
    "success": true,        // if any error occures, returns false
    "section_id": 1,        // reserved section id, if not reserved, returns "section_id": null
    "valid": true,          // if phone number is valid, returns true, else returns false
    "message": "Employee notified successfully"    // success or error message
}
```

**Functionality:**
- after request, it sends request to jetveo server to "/api/employment/reclaim" to check if reservation pin is valid, request: {"reservation_pin": "1asfd789"}, response: {"success": true, "phone_number": "123456789","valid": true, "section_id": null/1}
- if phone number is valid, it creates websocket session.
- calls function select_sections():
  success, selected_section_id = select_sections()
  - if success==true, it creates new record in order_reservation and saves phone_number and selected_section_id. Then sends message {"order_send": true}
  - if success==false, it just sends {"order_send": false, "message": some_error_message}
- closes websocket session

### /order/customer/send

**Request body:**
```json
{
    "reservation_pin": 123456
}
```

**Response body:**
```json
{
    "success": true,        // if any error occures, returns false
    "session_id": "1a219d3e-ed9b-411a-92c3-96c11b0bfda5",       // create new websocket session and returns session id
    "section_id": 1,       // if reserved section exists, returns it, else returns null
    "message": "Order found, ready for pickup"    // success or error message
}
```

**Functionality:**
- check if in order_reservations is existing record with status==8 (ready for insert) and with the same reservation pin
- if exists, create new websocket session
- calls function select_sections(wait_for_stop=false):
  success, selected_section_id = select_sections()
  - if success==true, it update status of record in order_reservation to 1 and saves selected_section_id. Then send message {"order_send": true}
  - if success==false, it just sends {"order_send": false, "message": some_error_message}
- closes websocket session

## Used Function Basic Logic

### pickup_process(sections: List[int]) -> bool, List[int]

```python
picking_up = True
successfull section = []
while picking_up:
    message = wait_for_message()   # wait for message from websocket. {"type":"open_section", "section_id": 1} or {"type": "storno"}
    match message["type"]:
        case "hardware_screen_ready":     #start pickup sequention and open all sections automaticly each after each
            for section in sections:
                send_message({"type": "opening_section", "section_id": section})
                open_door(section)
                send_message({"type": "section_status_changed", "status": "opened"})
                while check_door_state(section) == 1:     # while door is opened
                    wait
                send_message({"type": "section_status_changed", "status": "closed"})
                successfull_section.append(section)

        case "open_section":
            if message["section_id"] not in sections:
                send_message({"type": "error", "message": "Invalid section"})
                continue
            open_door(message["section_id"])
            while check_door_state(message["section_id"]) == 1:     # while door is opened
                wait
        case "storno":      # stop pickup sequence
            picking_up = False
            return True, successfull_section
```

### select_sections(wait_for_stop=true)

Function to choose section, returns success (if section was successfully selected), section_id (if section was successfully selected) and error_message (if section was not successfully selected)

```python
selected_section_ids = []
selecting = True
while selecting:
    message = wait_for_message()   # wait for message from websocket. {"type":"open_section", "section_id": 1} or {"type": "stop_selection"}

    if message["type"] == "open_section":
        open_door(message["section_id"])
        send_message({"section_id": message["section_id"], "status": "opened"})
        while check_door_state(message["section_id"]) == 1:     # while door is opened
            wait
        send_message({"section_id": message["section_id"], "status": "closed"})
        message = wait_for_message()        # {"inserted": true}
        if message["inserted"]:
            selected_section_ids.append(message["section_id"])
            return true, selected_section_ids
            if wait_for_stop:
                selecting = false
                return true, selected_section_ids
        elif not message["inserted"]:
            send_message = {"message": "waiting for new message"}
    
    elif message["type"] == "stop_selection":
        selecting = False
        return false, null
```

## Order Reservation Types

order_reservation "type" column types:
- employee_send - order, which employee sends to courier
- employee_deliver - order, which courier delivers to customer

## Mock Mode Data

Mock mode valid phone numbers: (123456789, 987654321, 555666777)
Mock mode valid reclamation PINs: (RECLAIM123, RECLAIM456)

## Jetveo API Documentation

### /api/employment/send
Checks phone number, when employee wants to send order to courier

**Request:**
```json
{"phone_number": "123456789"}
```

**Response:**
```json
{"success": true, "phone_number": "123456789","valid": true, "section_id": null/1}
```

### /api/employment/deliver
Checks phone number, when courier wants to deliver order to customer

**Request:**
```json
{"phone_number": "123456789"}
```

**Response:**
```json
{"success": true, "phone_number": "123456789","valid": true, "section_ids": null/[1,2,3]}
```

### /api/employment/reclaim
Checks reclamation pin, when customer wants to reclaim order from courier

**Request:**
```json
{"reservation_pin": "1asfd789"}
```

**Response:**
```json
{"success": true, "phone_number": "123456789","valid": true, "section_id": null/1}
